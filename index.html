<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- SEO Meta Tags -->
    <title><PERSON><PERSON> – Book Appointments & Manage Queues in Algeria</title>
    <meta name="description" content="Dalti is an Algerian booking app that connects providers and customers. Manage appointments, track queues, and save time with <PERSON><PERSON>.">
    <meta name="keywords" content="Dalti Algeria, appointment app Algeria, booking app Algeria, queue management app, online booking, doctor booking Algeria, salon booking app, mechanic booking app">
    <meta name="author" content="<PERSON><PERSON>">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Social Media Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Dalti – Book Appointments & Manage Queues in Algeria">
    <meta property="og:description" content="Dalti is an Algerian booking app that connects providers and customers. Manage appointments, track queues, and save time with <PERSON><PERSON>.">
    <meta property="og:url" content="https://dalti.com">
    <meta property="og:site_name" content="<PERSON>ti">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Dalti – Book Appointments & Manage Queues in Algeria">
    <meta name="twitter:description" content="Dalti is an Algerian booking app that connects providers and customers. Manage appointments, track queues, and save time with Dalti.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dalti-primary': '#15424E',
                        'dalti-light': '#F9FAFB',
                        'dalti-accent': '#2DD4BF'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        html {
            scroll-behavior: smooth;
        }

        /* Mobile menu animation */
        .mobile-menu {
            transition: all 0.3s ease-in-out;
        }

        /* Hover animations */
        .hover-lift {
            transition: transform 0.2s ease-in-out;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
        }

        /* Loading animation for images */
        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive text sizing */
        @media (max-width: 640px) {
            .hero-title {
                font-size: 2.5rem;
                line-height: 1.1;
            }
        }
    </style>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Dalti",
        "url": "https://dalti.com",
        "description": "Dalti is a smart appointment and queue management platform that helps service providers organize their schedules and customers book services without wasting time.",
        "foundingLocation": {
            "@type": "Place",
            "name": "Algeria"
        },
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "iOS, Android",
        "sameAs": [
            "https://provider.dalti.com",
            "https://customer.dalti.com"
        ]
    }
    </script>

    <!-- FAQ Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "What is Dalti?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Dalti is a smart appointment and queue management platform that helps service providers organize their schedules and customers book services without wasting time."
                }
            },
            {
                "@type": "Question",
                "name": "How does Dalti work?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Dalti works in three simple steps: 1) Providers list their services and manage schedules, 2) Customers book appointments instantly, 3) Both parties benefit from real-time queue tracking and reminders."
                }
            },
            {
                "@type": "Question",
                "name": "What industries does Dalti serve?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Dalti serves various industries including Healthcare (Doctors, Dentists), Beauty (Salons, Spas), Automotive (Mechanics, Car Wash), Fitness (Gyms, Trainers), and Legal (Lawyers, Notaries)."
                }
            }
        ]
    }
    </script>
</head>
<body class="font-sans bg-white text-gray-900 antialiased">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="text-2xl font-bold text-dalti-primary">Dalti</div>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#how-it-works" class="text-gray-600 hover:text-dalti-primary transition-colors">How It Works</a>
                    <a href="#industries" class="text-gray-600 hover:text-dalti-primary transition-colors">Industries</a>
                    <a href="#download" class="text-gray-600 hover:text-dalti-primary transition-colors">Download</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="https://provider.dalti.com" class="hidden sm:inline-block bg-dalti-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors text-sm font-medium">
                        For Providers
                    </a>
                    <!-- Mobile menu button -->
                    <button id="mobile-menu-button" class="md:hidden text-gray-600 hover:text-dalti-primary" aria-label="Toggle mobile menu" aria-expanded="false">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t">
                    <a href="#how-it-works" class="block px-3 py-2 text-gray-600 hover:text-dalti-primary transition-colors">How It Works</a>
                    <a href="#industries" class="block px-3 py-2 text-gray-600 hover:text-dalti-primary transition-colors">Industries</a>
                    <a href="#download" class="block px-3 py-2 text-gray-600 hover:text-dalti-primary transition-colors">Download</a>
                    <a href="https://provider.dalti.com" class="block px-3 py-2 bg-dalti-primary text-white rounded-lg hover:bg-opacity-90 transition-colors text-sm font-medium text-center mx-3 mt-4">
                        For Providers
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="bg-gradient-to-br from-dalti-light to-white py-20 lg:py-32">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <!-- Hero Content -->
                    <div class="text-center lg:text-left">
                        <h1 class="hero-title text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                            Your Time, Your Turn,
                            <span class="text-dalti-primary">Simplified with Dalti</span>
                        </h1>
                        <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                            Dalti connects service providers with customers for easy booking, smart scheduling, and real-time queue tracking.
                        </p>
                        
                        <!-- Primary CTAs -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                            <a href="https://provider.dalti.com" 
                               class="bg-dalti-primary text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-opacity-90 transition-all transform hover:scale-105 shadow-lg">
                                I'm a Provider
                            </a>
                            <a href="https://customer.dalti.com" 
                               class="bg-white text-dalti-primary border-2 border-dalti-primary px-8 py-4 rounded-xl font-semibold text-lg hover:bg-dalti-primary hover:text-white transition-all transform hover:scale-105 shadow-lg">
                                I'm a Customer
                            </a>
                        </div>
                    </div>
                    
                    <!-- Hero Visual -->
                    <div class="relative">
                        <div class="bg-gradient-to-r from-dalti-primary to-dalti-accent rounded-3xl p-8 shadow-2xl">
                            <div class="grid grid-cols-2 gap-6">
                                <!-- Provider Side -->
                                <div class="bg-white rounded-2xl p-6 text-center">
                                    <div class="w-16 h-16 bg-dalti-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 mb-2">Providers</h3>
                                    <p class="text-sm text-gray-600">Manage schedules & services</p>
                                </div>
                                
                                <!-- Customer Side -->
                                <div class="bg-white rounded-2xl p-6 text-center">
                                    <div class="w-16 h-16 bg-dalti-accent rounded-full mx-auto mb-4 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 mb-2">Customers</h3>
                                    <p class="text-sm text-gray-600">Book & track appointments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- What is Dalti Section -->
        <section class="py-20 bg-white">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">What is Dalti?</h2>
                <p class="text-xl text-gray-600 leading-relaxed mb-12">
                    Dalti is a smart appointment and queue management platform that helps service providers organize their schedules and customers book services without wasting time.
                </p>
                
                <!-- App Interface Mockup Placeholder -->
                <div class="bg-gradient-to-br from-dalti-light to-gray-100 rounded-3xl p-12 shadow-lg">
                    <div class="max-w-md mx-auto bg-white rounded-2xl shadow-xl p-6">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-dalti-primary rounded-full mx-auto mb-6 flex items-center justify-center">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Dalti App Interface</h3>
                            <p class="text-gray-600">Simple, intuitive, and powerful</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section id="how-it-works" class="py-20 bg-dalti-light">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Simple steps to connect providers and customers, making appointment booking effortless for everyone.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8">
                    <!-- Step 1: Provider -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg text-center hover:shadow-xl transition-shadow">
                        <div class="w-20 h-20 bg-dalti-primary rounded-full mx-auto mb-6 flex items-center justify-center">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="bg-dalti-primary text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold">1</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Providers List Services</h3>
                        <p class="text-gray-600 leading-relaxed">
                            Service providers create their profiles, list their services, and set up their availability schedules on the platform.
                        </p>
                    </div>

                    <!-- Step 2: Customer -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg text-center hover:shadow-xl transition-shadow">
                        <div class="w-20 h-20 bg-dalti-accent rounded-full mx-auto mb-6 flex items-center justify-center">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="bg-dalti-accent text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold">2</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Customers Book Instantly</h3>
                        <p class="text-gray-600 leading-relaxed">
                            Customers browse available services, select their preferred time slots, and book appointments instantly with just a few taps.
                        </p>
                    </div>

                    <!-- Step 3: Queue Tracking -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg text-center hover:shadow-xl transition-shadow">
                        <div class="w-20 h-20 bg-green-500 rounded-full mx-auto mb-6 flex items-center justify-center">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold">3</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Real-Time Queue Tracking</h3>
                        <p class="text-gray-600 leading-relaxed">
                            Both providers and customers get real-time updates, reminders, and queue tracking to save everyone's valuable time.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Industries Section -->
        <section id="industries" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Built for Every Industry</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        From healthcare to beauty, automotive to fitness, Dalti serves diverse industries across Algeria.
                    </p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                    <!-- Healthcare -->
                    <div class="bg-dalti-light rounded-2xl p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-red-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Healthcare</h3>
                        <p class="text-sm text-gray-600">Doctors, Dentists</p>
                    </div>

                    <!-- Beauty -->
                    <div class="bg-dalti-light rounded-2xl p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-pink-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Beauty</h3>
                        <p class="text-sm text-gray-600">Salons, Spas</p>
                    </div>

                    <!-- Automotive -->
                    <div class="bg-dalti-light rounded-2xl p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Automotive</h3>
                        <p class="text-sm text-gray-600">Mechanics, Car Wash</p>
                    </div>

                    <!-- Fitness -->
                    <div class="bg-dalti-light rounded-2xl p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-green-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Fitness</h3>
                        <p class="text-sm text-gray-600">Gyms, Trainers</p>
                    </div>

                    <!-- Legal -->
                    <div class="bg-dalti-light rounded-2xl p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Legal</h3>
                        <p class="text-sm text-gray-600">Lawyers, Notaries</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Social Proof Section -->
        <section class="py-20 bg-dalti-light">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Trusted by Providers and Loved by Customers</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Join hundreds of businesses and thousands of customers in Algeria who trust Dalti for their appointment management.
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                    <!-- Provider Testimonial -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-dalti-primary rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Dr. Amina Benali</h4>
                                <p class="text-gray-600 text-sm">Dentist, Algiers</p>
                            </div>
                        </div>
                        <blockquote class="text-gray-700 italic leading-relaxed">
                            "Dalti has transformed how I manage my practice. No more phone calls for appointments, and my patients love the queue tracking feature. It's made everything so much more efficient."
                        </blockquote>
                        <div class="flex text-yellow-400 mt-4">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Customer Testimonial -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-dalti-accent rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Yacine Meziani</h4>
                                <p class="text-gray-600 text-sm">Customer, Oran</p>
                            </div>
                        </div>
                        <blockquote class="text-gray-700 italic leading-relaxed">
                            "Finally, no more waiting in long queues! I can book my appointments and track exactly when it's my turn. Dalti has saved me so much time and stress."
                        </blockquote>
                        <div class="flex text-yellow-400 mt-4">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- App Download Section -->
        <section id="download" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Download Dalti and Save Your Time</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Get started today with the Dalti app. Available for both providers and customers on iOS and Android.
                    </p>
                </div>

                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <!-- App Download Content -->
                    <div class="text-center lg:text-left">
                        <div class="mb-8">
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">For Providers</h3>
                            <p class="text-gray-600 mb-6">Manage your schedule, track appointments, and grow your business with Dalti Provider App.</p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                                <a href="#" class="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
                                    <svg class="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z"/>
                                    </svg>
                                    App Store
                                </a>
                                <a href="#" class="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
                                    <svg class="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                                    </svg>
                                    Google Play
                                </a>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-2xl font-semibold text-gray-900 mb-4">For Customers</h3>
                            <p class="text-gray-600 mb-6">Book appointments, track queues, and never wait unnecessarily with Dalti Customer App.</p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                <a href="#" class="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
                                    <svg class="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z"/>
                                    </svg>
                                    App Store
                                </a>
                                <a href="#" class="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
                                    <svg class="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                                    </svg>
                                    Google Play
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Phone Mockups -->
                    <div class="relative">
                        <div class="flex justify-center space-x-6">
                            <!-- Provider App Mockup -->
                            <div class="bg-gradient-to-br from-dalti-primary to-dalti-accent rounded-3xl p-6 shadow-2xl transform rotate-3">
                                <div class="bg-white rounded-2xl p-6 w-48">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-dalti-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <h4 class="font-semibold text-gray-900 mb-2">Provider App</h4>
                                        <p class="text-sm text-gray-600">Manage your business</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer App Mockup -->
                            <div class="bg-gradient-to-br from-dalti-accent to-green-400 rounded-3xl p-6 shadow-2xl transform -rotate-3">
                                <div class="bg-white rounded-2xl p-6 w-48">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-dalti-accent rounded-full mx-auto mb-4 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <h4 class="font-semibold text-gray-900 mb-2">Customer App</h4>
                                        <p class="text-sm text-gray-600">Book appointments</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <footer class="bg-dalti-primary text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="text-2xl font-bold mb-4">Dalti</div>
            <p class="text-gray-300 mb-6">Your Time, Your Turn, Simplified</p>
            <div class="flex justify-center space-x-6 mb-6">
                <a href="https://provider.dalti.com" class="text-gray-300 hover:text-white transition-colors">Provider App</a>
                <a href="https://customer.dalti.com" class="text-gray-300 hover:text-white transition-colors">Customer App</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">About</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">Support</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a>
            </div>
            <p class="text-gray-400 text-sm">© 2025 Dalti. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking on links
        const mobileMenuLinks = mobileMenu.querySelectorAll('a');
        mobileMenuLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add fade-in animation to sections on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
                mobileMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
