PRD: Dalti.com – General Landing Page
1. Overview

Dalti.com is the main landing page of the Dalti ecosystem.
It introduces <PERSON><PERSON>, explains how it works, and funnels users to either the Provider App or Customer App pages.

The design should be clean, modern, and mobile-first, using HTML + Tailwind CSS.
Focus on clarity, speed, and SEO optimization.

2. Objectives

Clearly explain what <PERSON><PERSON> is (bridge between providers and customers).

Highlight value propositions for both providers and customers.

Funnel users to the correct app (Provider / Customer).

Drive App Downloads (App Store + Google Play links).

Ensure SEO discoverability (Algerian + global audience).

3. Target Audience

Service Providers (Doctors, Salons, Mechanics, Lawyers, Trainers, etc.)

Customers (everyday users looking to book services easily)

4. Page Structure & Content
4.1 Hero Section

Headline: “Your Time, Your Turn, Simplified with <PERSON><PERSON>.”

Subheadline: “<PERSON><PERSON> connects service providers with customers for easy booking, smart scheduling, and real-time queue tracking.”

Primary CTAs:

Button 1 → “I’m a Provider” → provider.dalti.com

Button 2 → “I’m a Customer” → customer.dalti.com

Visual: Split illustration (left: provider managing calendar, right: customer checking phone).

SEO Keywords: Dalti Algeria, online booking app, appointment app, queue management app, service booking Algeria.

4.2 What is <PERSON><PERSON>? (Intro Section)

Text:
“Dalti is a smart appointment and queue management platform that helps service providers organize their schedules and customers book services without wasting time.”

Visual: Simple illustration or mockup of Dalti app interface.

4.3 How It Works (3-Step Process)

Step 1 (Provider): Providers list services & manage schedules.

Step 2 (Customer): Customers book appointments instantly.

Step 3 (Both): Queue tracking & reminders save everyone time.

Design: 3 cards/icons in a horizontal layout (stacked on mobile).

4.4 Industries / Categories

Headline: “Built for Every Industry”

Categories (grid layout):

Healthcare (Doctors, Dentists)

Beauty (Salons, Spas)

Automotive (Mechanics, Car Wash)

Fitness (Gyms, Trainers)

Legal (Lawyers, Notaries)

Design: Icons + labels inside Tailwind cards.

4.5 Social Proof / Testimonials

Headline: “Trusted by Providers and Loved by Customers”

Content:

One provider testimonial.

One customer testimonial.

Optional: “Join hundreds of businesses and thousands of customers in Algeria.”

4.6 Call to Action (App Download)

Headline: “Download Dalti and Save Your Time”

Buttons:

Google Play Badge (Customer App + Provider App)

App Store Badge (Customer App + Provider App)

Visual: Phone mockups showing Dalti app.

4.7 Footer

Links: Provider App | Customer App | About | Support | Privacy Policy

Copyright: “© 2025 Dalti. All rights reserved.”

5. Design Concept

Color Palette:

Primary: #15424E (deep teal green).

Accent: Lighter teal/white gradients.

Background: White with soft gray (#F9FAFB).

Typography:

Headings: Bold, modern sans-serif (e.g., Inter, Poppins, or Tailwind default).

Body: Clean, legible sans-serif.

Layout:

Mobile-first, responsive.

Sections with lots of white space.

Rounded cards (rounded-2xl) with soft shadows (shadow-lg).

Visual Style:

Minimalistic, with custom illustrations or icons.

Use mockups of the Dalti mobile app where possible.

6. SEO Optimization

Meta Title: “Dalti – Book Appointments & Manage Queues in Algeria”

Meta Description: “Dalti is an Algerian booking app that connects providers and customers. Manage appointments, track queues, and save time with Dalti.”

Keywords:

Primary: Dalti Algeria, appointment app Algeria, booking app Algeria, queue management app, online booking.

Secondary: doctor booking Algeria, salon booking app, mechanic booking app.

Technical SEO:

Use <h1> for Hero headline, <h2> for section titles.

Structured schema (FAQ / Organization).

Fast load time (Tailwind + optimized images).

Mobile-first design.

Open Graph / Social Sharing Tags:

og:title, og:description, og:image (app mockup), og:url.

7. Implementation Notes (for AI Coder)

Framework: Pure HTML + Tailwind CSS (and JS if needed).

Responsive grid system for layout.

Accessibility: Alt text for all images.

Reusable Tailwind utility classes.

Lazy-load images for performance.

CTA buttons should have hover states.

Use SVG icons for scalability.

✅ Deliverable: A fully functional dalti.com landing page in HTML + Tailwind, mobile-optimized, SEO-ready, and visually consistent with Dalti’s brand.